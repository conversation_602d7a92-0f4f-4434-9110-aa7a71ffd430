<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg p-4 sm:p-6 max-w-4xl w-full" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg sm:text-xl font-semibold">E-Certificate</h3>
        <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Certificate Image -->
      <div class="mb-6">
        <canvas
          ref="certificateCanvas"
          class="w-full h-auto border border-gray-200 rounded-lg shadow-sm"
          :width="800"
          :height="600"
        ></canvas>
      </div>

      <!-- Download Button -->
      <div class="flex justify-center">
        <button
          @click="downloadCertificate"
          class="bg-orange text-white px-6 py-3 rounded-md hover:bg-orange-dark transition-colors flex items-center text-sm font-medium"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Download Certificate
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CertificateView',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    classId: {
      type: [String, Number],
      required: true
    },
    classTitle: {
      type: String,
      required: true
    },
    studentName: {
      type: String,
      default: 'Student Name'
    },
    completionDate: {
      type: String,
      default: null
    }
  },
  emits: ['close', 'download'],
  computed: {
    formattedDate() {
      if (this.completionDate) {
        return this.completionDate;
      }

      // Default to current date if no completion date provided
      const now = new Date();
      const day = String(now.getDate()).padStart(2, '0');
      const month = now.toLocaleString('default', { month: 'long' });
      const year = now.getFullYear();

      return `${day} ${month} ${year}`;
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.drawCertificate();
        });
      }
    }
  },
  mounted() {
    if (this.show) {
      this.drawCertificate();
    }
  },
  methods: {
    drawCertificate() {
      const canvas = this.$refs.certificateCanvas;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      const width = canvas.width;
      const height = canvas.height;

      // Clear canvas
      ctx.clearRect(0, 0, width, height);

      // Set background gradient
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, '#FFF7ED'); // orange-50
      gradient.addColorStop(1, '#FFFFFF'); // white
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);

      // Draw border
      ctx.strokeStyle = '#FED7AA'; // orange-200
      ctx.lineWidth = 8;
      ctx.strokeRect(20, 20, width - 40, height - 40);

      // Set text properties
      ctx.textAlign = 'center';
      ctx.fillStyle = '#1F2937'; // gray-800

      // Draw title
      ctx.font = 'bold 36px Arial, sans-serif';
      ctx.fillText('CERTIFICATE OF COMPLETION', width / 2, 120);

      // Draw "This certifies that" text
      ctx.font = '18px Arial, sans-serif';
      ctx.fillStyle = '#6B7280'; // gray-500
      ctx.fillText('This certifies that', width / 2, 180);

      // Draw student name
      ctx.font = 'bold 28px Arial, sans-serif';
      ctx.fillStyle = '#EA580C'; // orange-600
      ctx.fillText(this.studentName, width / 2, 230);

      // Draw "has successfully completed the course" text
      ctx.font = '18px Arial, sans-serif';
      ctx.fillStyle = '#6B7280'; // gray-500
      ctx.fillText('has successfully completed the course', width / 2, 280);

      // Draw class title
      ctx.font = 'bold 24px Arial, sans-serif';
      ctx.fillStyle = '#1F2937'; // gray-800
      ctx.fillText(this.classTitle, width / 2, 330);

      // Draw decorative line with checkmark
      ctx.strokeStyle = '#D1D5DB'; // gray-300
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(width / 2 - 80, 380);
      ctx.lineTo(width / 2 - 20, 380);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(width / 2 + 20, 380);
      ctx.lineTo(width / 2 + 80, 380);
      ctx.stroke();

      // Draw checkmark
      ctx.strokeStyle = '#EA580C'; // orange-600
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.moveTo(width / 2 - 8, 380);
      ctx.lineTo(width / 2 - 2, 386);
      ctx.lineTo(width / 2 + 8, 374);
      ctx.stroke();

      // Draw date section
      ctx.font = '14px Arial, sans-serif';
      ctx.fillStyle = '#6B7280'; // gray-500
      ctx.textAlign = 'left';
      ctx.fillText('Date', 150, 480);
      ctx.font = 'bold 16px Arial, sans-serif';
      ctx.fillStyle = '#1F2937'; // gray-800
      ctx.fillText(this.formattedDate, 150, 500);

      // Draw signature section
      ctx.font = '14px Arial, sans-serif';
      ctx.fillStyle = '#6B7280'; // gray-500
      ctx.textAlign = 'right';
      ctx.fillText('Director', width - 150, 480);
      ctx.font = 'bold 16px Arial, sans-serif';
      ctx.fillStyle = '#1F2937'; // gray-800
      ctx.fillText('FlowCamp', width - 150, 500);

      // Draw signature line
      ctx.strokeStyle = '#D1D5DB'; // gray-300
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(width - 220, 470);
      ctx.lineTo(width - 80, 470);
      ctx.stroke();
    },

    downloadCertificate() {
      // In a real application, this would generate and download a PDF certificate
      // For now, we'll just emit an event to show the success dialog

      // Validate class ID
      if (!this.classId) {
        console.error('Cannot download certificate: Missing class ID');
        return;
      }

      console.log(`Downloading certificate for class ${this.classId}: ${this.classTitle}`);

      // Emit download event with class ID
      this.$emit('download', this.classId);
    }
  }
}
</script>
