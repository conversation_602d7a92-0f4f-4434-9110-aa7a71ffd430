<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg p-4 sm:p-6 max-w-4xl w-full" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg sm:text-xl font-semibold">E-Certificate</h3>
        <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Certificate Image -->
      <div class="mb-6">
        <div class="relative">
          <img
            :src="finalCertificateImageUrl"
            :alt="`Certificate for ${classTitle}`"
            class="w-full h-auto border border-gray-200 rounded-lg shadow-sm"
            @error="handleImageError"
            @load="handleImageLoad"
          />

          <!-- Loading state -->
          <div v-if="imageLoading" class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange mx-auto mb-2"></div>
              <p class="text-sm text-gray-600">Loading certificate...</p>
            </div>
          </div>

          <!-- Error state -->
          <div v-if="imageError" class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <div class="text-center p-8">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p class="text-sm text-gray-600 mb-2">Certificate image not available</p>
              <p class="text-xs text-gray-500">Please contact administrator</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Download Button -->
      <div class="flex justify-center">
        <button
          @click="downloadCertificate"
          :disabled="imageError"
          class="bg-orange text-white px-6 py-3 rounded-md hover:bg-orange-dark transition-colors flex items-center text-sm font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Download Certificate
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CertificateView',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    classId: {
      type: [String, Number],
      required: true
    },
    classTitle: {
      type: String,
      required: true
    },
    studentName: {
      type: String,
      default: 'Student Name'
    },
    completionDate: {
      type: String,
      default: null
    },
    certificateImageUrl: {
      type: String,
      default: null
    }
  },
  emits: ['close', 'download'],
  data() {
    return {
      imageLoading: true,
      imageError: false
    };
  },
  computed: {
    formattedDate() {
      if (this.completionDate) {
        return this.completionDate;
      }

      // Default to current date if no completion date provided
      const now = new Date();
      const day = String(now.getDate()).padStart(2, '0');
      const month = now.toLocaleString('default', { month: 'long' });
      const year = now.getFullYear();

      return `${day} ${month} ${year}`;
    },

    // Generate certificate image URL based on class data
    finalCertificateImageUrl() {
      // If certificateImageUrl prop is provided, use it
      if (this.certificateImageUrl) {
        return this.certificateImageUrl;
      }

      // Generate certificate image path based on classId
      // Admin uploads certificates with naming convention: certificate-{classId}.jpg/png
      // In a real application, this would be handled by the backend
      return `/certificates/certificate-${this.classId}.jpg`;
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        // Reset image states when modal is shown
        this.imageLoading = true;
        this.imageError = false;
      }
    }
  },
  methods: {
    handleImageLoad() {
      this.imageLoading = false;
      this.imageError = false;
    },

    handleImageError() {
      this.imageLoading = false;
      this.imageError = true;
      console.warn(`Certificate image not found for class ${this.classId}: ${this.finalCertificateImageUrl}`);
    },

    downloadCertificate() {
      // Validate class ID and image availability
      if (!this.classId) {
        console.error('Cannot download certificate: Missing class ID');
        return;
      }

      if (this.imageError) {
        console.error('Cannot download certificate: Image not available');
        return;
      }

      console.log(`Downloading certificate for class ${this.classId}: ${this.classTitle}`);

      // In a real application, this would trigger the actual download
      // For now, we'll just emit an event to show the success dialog
      this.$emit('download', this.classId);
    }
  }
}
</script>
